import os
import sys
import json
import shutil
import time
import datetime
import schedule
import tkinter as tk
from tkinter import ttk, scrolledtext, font
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import ctypes
import traceback

# 设置程序图标
try:
    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID('myappid.QAR_Datamation')
except:
    pass

# 确保中文显示正常
tk.font.families()

class CountManager:
    def __init__(self, file_path='count.json'):
        self.file_path = file_path
        # 确保文件存在并初始化
        if not os.path.exists(file_path):
            self._create_default_file()

    def _create_default_file(self):
        default_data = {
            'pc_count': 0,
            'enc_count': 0
        }
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, ensure_ascii=False, indent=2)

    def get_pc_count(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('pc_count', 0)
        except:
            return 0

    def set_pc_count(self, count):
        try:
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['pc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()
        except:
            # 如果读取失败，创建新文件
            self._create_default_file()
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['pc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()

    def get_enc_count(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('enc_count', 0)
        except:
            return 0

    def set_enc_count(self, count):
        try:
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['enc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()
        except:
            # 如果读取失败，创建新文件
            self._create_default_file()
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['enc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()

class TimeManager:
    def __init__(self, file_path='last_time.json'):
        self.file_path = file_path
        # 确保文件存在并初始化
        if not os.path.exists(file_path):
            self._create_default_file()

    def _create_default_file(self):
        default_data = {
            'last_run_time': ''
        }
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, ensure_ascii=False, indent=2)

    def get_last_run_time(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('last_run_time', '')
        except:
            return ''

    def set_last_run_time(self, run_time):
        try:
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['last_run_time'] = run_time
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()
        except:
            # 如果读取失败，创建新文件
            self._create_default_file()
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['last_run_time'] = run_time
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()

    # 为兼容旧代码保留的方法
    def get_last_timestamp(self):
        last_run_time = self.get_last_run_time()
        if last_run_time:
            try:
                dt = datetime.datetime.strptime(last_run_time, '%Y-%m-%d %H:%M:%S')
                return int(dt.timestamp())
            except:
                pass
        return 0

    def set_last_timestamp(self, timestamp):
        # 转换时间戳为具体时间格式
        run_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        self.set_last_run_time(run_time)

class FolderMonitor:
    def __init__(self, path, callback, is_pc=True):
        self.path = path
        self.callback = callback
        self.is_pc = is_pc
        self.observer = Observer()
        self.event_handler = self._create_event_handler()

    def _create_event_handler(self):
        class Handler(FileSystemEventHandler):
            def __init__(self, monitor):
                self.monitor = monitor

            def on_created(self, event):
                if event.is_directory:
                    # 只处理新创建的文件夹
                    self.monitor.callback(event.src_path, self.monitor.is_pc)

        return Handler(self)

    def start(self):
        if os.path.exists(self.path):
            self.observer.schedule(self.event_handler, self.path, recursive=False)
            self.observer.start()
            return True
        return False

    def stop(self):
        if self.observer.is_alive():
            self.observer.stop()
            self.observer.join()

class DataProcessor:
    def __init__(self, app):
        self.app = app
        self.time_manager = TimeManager('last_time.json')
        self.ac_type_map = self._load_ac_type_map()
        self.processed_folders = set()

    def get_last_run_timestamp(self):
        return self.time_manager.get_last_timestamp()

    def _load_ac_type_map(self):
        try:
            with open('Z:\DATA_BAK\config\AC_TYPE.json', 'r', encoding='utf-8-sig') as f:
                return json.load(f)
        except Exception as e:
            self.app.log_message(f'错误 - 无法加载AC_TYPE.json: {str(e)}')
            return {}

    def check_folder_written(self, folder_path):
        # 检查文件夹是否写入完毕（连续三次0.5秒内大小不变则认为写入完毕）
        folder_name = os.path.basename(folder_path)
        prev_size = -1
        stable_count = 0
        while stable_count < 3:
            current_size = self._get_folder_size(folder_path)
            if current_size == prev_size:
                stable_count += 1
            else:
                stable_count = 0
                prev_size = current_size
            time.sleep(0.5)
        self.app.log_message(f'信息 - {folder_name} 文件写入完毕')
        return True

    def _get_folder_size(self, folder_path):
        total_size = 0
        for dirpath, _, filenames in os.walk(folder_path):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                if os.path.isfile(fp):
                    total_size += os.path.getsize(fp)
        return total_size

    def process_folder(self, folder_path, is_pc):
        self.is_pc = is_pc
        folder_name = os.path.basename(folder_path)
        self.app.log_message(f'开始处理文件夹 [{folder_name}]')

        # 检查是否已处理过
        if folder_name in self.processed_folders:
            self.app.log_message(f'跳过文件夹 [{folder_name}]，已处理过')
            return

        # 检查文件夹创建时间
        try:
            created_time = os.path.getctime(folder_path)
            last_run_timestamp = self.get_last_run_timestamp()
            if created_time < last_run_timestamp:
                # 忽略上次运行前创建的文件夹
                return
        except Exception as e:
            self.app.log_message(f'错误 - 无法获取文件夹 [{folder_name}] 创建时间: {str(e)}')
            # 即使无法获取创建时间，也继续处理该文件夹

        self.app.update_status(f'发现{"PC" if is_pc else "ENC"}新文件 [{folder_name}]')

        # 识别飞机号
        if len(folder_name) >= 6:
            ac_tail = folder_name[:6]
            if ac_tail.startswith('B-') and len(ac_tail) == 6:
                # 查找机型
                ac_type = self.ac_type_map.get(ac_tail, 'Unknown')
                if ac_type == 'Unknown':
                    self.app.log_message(f'警告 - 无法找到飞机号 {ac_tail} 对应的机型')
                    return

                # 检查文件是否写入完毕
                if not self.check_folder_written(folder_path):
                    return

                # 确定目标路径
                if is_pc:
                    # PC卡路径: D:\AirFASE\FIMRoot\[ac_type]\[ac_tail]\[folder_name]
                    target_path = os.path.join('D:\AirFASE\FIMRoot', ac_type, ac_tail, folder_name)
                    # 添加调试信息
                    self.app.log_message(f'调试 - 处理PC文件，文件夹名: {folder_name}，飞机号: {ac_tail}，机型: {ac_type}，目标路径: {target_path}')
                else:
                    # ENC文件处理
                    new_folder_name = folder_name + '.enc'
                    target_path = os.path.join('D:\AirFASE\FIMRoot', f'WGL_{ac_type}', ac_tail, new_folder_name)

                # 检查目标路径是否存在
                if os.path.exists(target_path):
                    return

                # 创建目标目录
                try:
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                except Exception as e:
                    self.app.log_message(f'无法创建目标目录: {str(e)}', level='error')
                    return

                # 复制文件夹
                self.app.update_status(f'正在复制{"PC" if is_pc else "ENC"}文件 [{folder_name}]')
                try:
                    # 复制整个文件夹
                    shutil.copytree(folder_path, target_path)
                    self.app.log_message(f'成功复制文件夹: {folder_name}', level='success')
                    self.processed_folders.add(folder_name)

                    # 更新统计 (以文件夹为单位)
                    if is_pc:
                        current_count = self.app.count_manager.get_pc_count() + 1
                        self.app.count_manager.set_pc_count(current_count)
                        self.app.update_pc_count(current_count)
                    else:
                        current_count = self.app.count_manager.get_enc_count() + 1
                        self.app.count_manager.set_enc_count(current_count)
                        self.app.update_enc_count(current_count)

                    # 复制完成后更新状态为等待文件
                    self.app.update_status('新文件持续监控中')

                    # 已移除文件夹处理完成统计日志
                except Exception as e:
                    self.app.log_message(f'处理文件夹 [{folder_name}] 时出错: {str(e)}', level='error')
                    self.app.log_message(f'错误详情: {traceback.format_exc()}', level='error')
                return
            else:
                self.app.log_message(f'警告 - 文件夹名 {folder_name} 不符合飞机号格式(B-xxxx)')
        else:
            self.app.log_message(f'警告 - 文件夹名 {folder_name} 长度不足，无法提取飞机号')

    def cleanup_old_data(self):
        # 清理前一天复制的数据
        self.app.update_status('正在执行定期清理任务')
        root_path = 'D:\AirFASE\FIMRoot'

        # 获取当前日期和前一天日期（日期格式：YYYY-MM-DD）
        today = datetime.datetime.now().date()
        yesterday = today - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')

        try:
            for ac_type in os.listdir(root_path):
                ac_type_path = os.path.join(root_path, ac_type)
                if os.path.isdir(ac_type_path):
                    for ac_tail in os.listdir(ac_type_path):
                        ac_tail_path = os.path.join(ac_type_path, ac_tail)
                        if os.path.isdir(ac_tail_path):
                            self.app.update_status(f'正在检查 {ac_type} 类型飞机 [{ac_tail}] 下的数据文件夹')
                            
                            # 遍历飞机号文件夹下的所有子文件夹
                            for sub_folder in os.listdir(ac_tail_path):
                                sub_folder_path = os.path.join(ac_tail_path, sub_folder)
                                if os.path.isdir(sub_folder_path):
                                    try:
                                        # 获取文件夹创建日期
                                        created_time = os.path.getctime(sub_folder_path)
                                        created_date = datetime.datetime.fromtimestamp(created_time).date()
                                        created_date_str = created_date.strftime('%Y-%m-%d')

                                        # 检查是否为前一天创建的文件夹
                                        if created_date == yesterday:
                                            self.app.update_status(f'正在删除 {ac_type} 类型飞机 [{ac_tail}] 下的过期数据文件夹 [{sub_folder}]')
                                            shutil.rmtree(sub_folder_path)
                                            self.app.log_message(f'已删除前一天数据文件夹: {sub_folder_path}', level='info')
                                    except Exception as e:
                                        self.app.log_message(f'无法删除文件夹 {sub_folder_path}: {str(e)}', level='error')
        except Exception as e:
            self.app.log_message(f'清理旧数据时发生异常: {str(e)}', level='error')

        self.app.update_status('新文件持续监控中')

class QARApp:
    def __init__(self, root):
        self.root = root
        self.root.title('QAR_Datamation 飞机QAR数据自动抓取工具')
        self.root.geometry('900x700')
        self.root.resizable(True, True)

        # 设置图标
        try:
            self.root.iconbitmap('app.ico')
        except Exception as e:
            print(f'无法设置图标: {e}')

        # 初始化配置管理器
        self.count_manager = CountManager('count.json')
        self.time_manager = TimeManager('last_time.json')
        self.pc_count = self.count_manager.get_pc_count()
        self.enc_count = self.count_manager.get_enc_count()

        # UI元素变量初始化
        self.status_var = None
        self.runtime_var = None
        self.last_run_var = None
        self.pc_count_var = None
        self.enc_count_var = None
        self.log_text = None
        self.run_button = None
        self.pause_button = None
        self.exit_button = None

        # 先创建UI，确保log_text已初始化
        self._create_ui()

        # 然后记录日志
        last_run_time = self.time_manager.get_last_run_time()
        if last_run_time:
            self.log_message(f'上次运行时间: {last_run_time}', level='info')
        else:
            self.log_message('首次运行程序', level='info')

        # 创建数据处理器
        self.data_processor = DataProcessor(self)

        # 创建监控器
        self.pc_monitor = FolderMonitor('Z:\DATA_BAK\QAR_PC', self.on_new_folder, is_pc=True)
        self.enc_monitor = FolderMonitor('Z:\DATA_BAK\ENC_BAK', self.on_new_folder, is_pc=False)

        # 启动时间计数
        self.start_time = time.time()
        self.update_runtime()

        # 默认状态为暂停
        self.is_running = False
        self.status_var.set('监控已暂停')
        self.pause_button.config(state=tk.DISABLED)
        self.run_button.config(state=tk.NORMAL)
        self.log_message('程序已初始化，监控处于暂停状态', level='info')

        # 设置定时任务
        schedule.every().day.at('06:00').do(self.data_processor.cleanup_old_data)
        self.start_schedule_thread()

        # 记录当前运行时间
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.log_message(f'当前运行时间: {current_time}')

    def _create_ui(self):
        # 设置中文字体支持
        default_font = ('Microsoft YaHei', 10)
        self.root.option_add('*Font', default_font)

        # 主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 顶部信息栏
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=5)

        # 程序标题
        title_label = ttk.Label(top_frame, text='QAR DATAMATION 飞机QAR数据自动抓取工具',
                               font=('Microsoft YaHei', 14, 'bold'), foreground='#2E7D32')
        title_label.pack(side=tk.LEFT, padx=10)

        # 运行时间
        self.runtime_var = tk.StringVar(value='运行时间: 00:00:00')
        ttk.Label(top_frame, textvariable=self.runtime_var,
                 font=('Microsoft YaHei', 10, 'bold')).pack(side=tk.RIGHT, padx=10)

        # 程序状态栏 - 使用卡片式设计
        status_frame = ttk.LabelFrame(main_frame, text='程序状态', padding=10)
        status_frame.pack(fill=tk.X, pady=5)

        status_inner_frame = ttk.Frame(status_frame)
        status_inner_frame.pack(fill=tk.X)

        ttk.Label(status_inner_frame, text='当前状态:', font=('Microsoft YaHei', 10, 'bold')).pack(side=tk.LEFT, padx=5)
        self.status_var = tk.StringVar(value='新文件持续监控中')
        ttk.Label(status_inner_frame, textvariable=self.status_var, foreground='blue',
                 font=('Microsoft YaHei', 10, 'bold')).pack(side=tk.LEFT, padx=5)

        # 上次运行时间
        last_run_time = self.time_manager.get_last_run_time()
        self.last_run_var = tk.StringVar(value=f'上次运行时间: {last_run_time if last_run_time else "首次运行"}')
        ttk.Label(status_inner_frame, textvariable=self.last_run_var,
                 font=('Microsoft YaHei', 10)).pack(side=tk.RIGHT, padx=5)

        # 数据统计 - 改进的卡片式设计
        stats_frame = ttk.LabelFrame(main_frame, text='数据统计', padding=10)
        stats_frame.pack(fill=tk.X, pady=5)

        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X, pady=5)

        # PC卡统计卡片
        pc_frame = ttk.Frame(stats_grid, relief=tk.RAISED, borderwidth=2)
        pc_frame.pack(side=tk.LEFT, padx=10, expand=True, fill=tk.BOTH)
        pc_frame.configure(style='Card.TFrame')

        ttk.Label(pc_frame, text='PC卡数据', font=('Microsoft YaHei', 11, 'bold')).pack(pady=5)
        self.pc_count_var = tk.StringVar(value=str(self.pc_count))
        ttk.Label(pc_frame, textvariable=self.pc_count_var, font=('Microsoft YaHei', 32, 'bold'),
                 foreground='#2E7D32').pack(pady=10)
        ttk.Label(pc_frame, text='个数据文件夹已处理', font=('Microsoft YaHei', 10)).pack(pady=5)

        # ENC文件统计卡片
        enc_frame = ttk.Frame(stats_grid, relief=tk.RAISED, borderwidth=2)
        enc_frame.pack(side=tk.LEFT, padx=10, expand=True, fill=tk.BOTH)
        enc_frame.configure(style='Card.TFrame')

        ttk.Label(enc_frame, text='ENC文件数据', font=('Microsoft YaHei', 11, 'bold')).pack(pady=5)
        self.enc_count_var = tk.StringVar(value=str(self.enc_count))
        ttk.Label(enc_frame, textvariable=self.enc_count_var, font=('Microsoft YaHei', 32, 'bold'),
                 foreground='#1976D2').pack(pady=10)
        ttk.Label(enc_frame, text='个数据文件夹已处理', font=('Microsoft YaHei', 10)).pack(pady=5)

        # 设置按钮样式
        self.style = ttk.Style()
        self.style.configure('TButton', font=('Microsoft YaHei', 10), padding=6, relief='raised')
        self.style.configure('Success.TButton', foreground='white', background='#4CAF50')
        self.style.configure('Warning.TButton', foreground='white', background='#FF9800')
        self.style.configure('Danger.TButton', foreground='white', background='#F44336')
        self.style.configure('Card.TFrame', background='#f0f0f0')

        # 按钮区域
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=10)

        # 运行按钮
        self.run_button = ttk.Button(buttons_frame, text='运行', command=self.start_monitoring, width=12,
                                    style='Success.TButton')
        self.run_button.pack(side=tk.LEFT, padx=5)

        # 暂停按钮
        self.pause_button = ttk.Button(buttons_frame, text='暂停', command=self.pause_monitoring, width=12,
                                     style='Warning.TButton')
        self.pause_button.pack(side=tk.LEFT, padx=5)
        self.pause_button.config(state=tk.DISABLED)  # 初始状态暂停按钮禁用

        # 退出按钮
        self.exit_button = ttk.Button(buttons_frame, text='退出', command=self.exit_app, width=12,
                                    style='Danger.TButton')
        self.exit_button.pack(side=tk.LEFT, padx=5)

        # 运行日志 - 改进的卡片式设计
        logs_frame = ttk.LabelFrame(main_frame, text='运行日志', padding=10)
        logs_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建带滚动条的文本区域
        log_frame = ttk.Frame(logs_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 使用ScrolledText自带的滚动条
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Microsoft YaHei', 10),
                                                 bg='#f8f8f8', fg='#333')
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 初始化日志
        self.log_message('应用程序初始化成功')

    def open_config(self):
        pass

    def update_status(self, status):
        self.status_var.set(status)
        self.log_message(f'信息 - {status}')

    def update_pc_count(self, count):
        self.pc_count = count
        self.pc_count_var.set(str(count))

    def update_enc_count(self, count):
        self.enc_count = count
        self.enc_count_var.set(str(count))

    def log_message(self, message, level='info'):
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        level_prefix = ''
        text_color = ''

        # 根据日志级别设置前缀和颜色
        if level == 'error':
            level_prefix = '[错误] ' 
            text_color = '#e74c3c'
        elif level == 'warning':
            level_prefix = '[警告] ' 
            text_color = '#f39c12'
        elif level == 'info':
            level_prefix = '[信息] ' 
            text_color = '#2c3e50'
        elif level == 'success':
            level_prefix = '[成功] ' 
            text_color = '#27ae60'

        log_entry = f'{timestamp} - {level_prefix}{message}'
        self.log_text.insert(tk.END, log_entry + '\n')

        # 为不同级别的日志设置不同颜色
        last_line = self.log_text.index('end-2l')
        self.log_text.tag_add(level, last_line, f'{last_line} lineend')
        self.log_text.tag_config(level, foreground=text_color)

        self.log_text.see(tk.END)
        print(log_entry, end='\n')  # 添加控制台输出

    def update_runtime(self):
        elapsed = int(time.time() - self.start_time)
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        self.runtime_var.set(f'运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}')
        self.root.after(1000, self.update_runtime)

    def on_new_folder(self, folder_path, is_pc):
        # 在新线程中处理文件夹
        threading.Thread(target=self.data_processor.process_folder, args=(folder_path, is_pc)).start()

    def start_monitoring(self):
        if not self.is_running:
            self.status_var.set('新文件持续监控中')
            self.log_message('开始监控新文件...')
            
            # 检查监控目录是否存在
            pc_path = 'Z:\DATA_BAK\QAR_PC'
            enc_path = 'Z:\DATA_BAK\ENC_BAK'
            self.log_message(f'检查PC监控目录: {pc_path}')
            if os.path.exists(pc_path):
                self.log_message(f'PC监控目录存在: {pc_path}')
                self.log_message(f'PC目录文件数量: {len(os.listdir(pc_path))}')
            else:
                self.log_message(f'错误 - PC监控目录不存在: {pc_path}')
            
            self.log_message(f'检查ENC监控目录: {enc_path}')
            if os.path.exists(enc_path):
                self.log_message(f'ENC监控目录存在: {enc_path}')
                self.log_message(f'ENC目录文件数量: {len(os.listdir(enc_path))}')
            else:
                self.log_message(f'错误 - ENC监控目录不存在: {enc_path}')
            
            # 更新按钮状态（运行时禁用开始按钮，启用暂停按钮）
            self.run_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            
            # 检查监控线程是否已启动，如果已启动则先停止
            if hasattr(self.pc_monitor, 'observer') and self.pc_monitor.observer.is_alive():
                self.pc_monitor.stop()
            
            if hasattr(self.enc_monitor, 'observer') and self.enc_monitor.observer.is_alive():
                self.enc_monitor.stop()
            
            # 重新创建监控器实例
            self.pc_monitor = FolderMonitor(pc_path, self.on_new_folder, is_pc=True)
            self.enc_monitor = FolderMonitor(enc_path, self.on_new_folder, is_pc=False)

            pc_started = self.pc_monitor.start()
            enc_started = self.enc_monitor.start()

            if pc_started and enc_started:
                self.is_running = True
                self.update_status('新文件持续监控中')
                self.log_message('监控已成功启动')

                # 扫描已有文件夹并处理
                self.scan_existing_folders()
            else:
                self.log_message('监控启动失败')
                if not pc_started:
                    self.log_message('错误 - 无法访问PC文件夹路径: Z:\DATA_BAK\QAR_PC')
                if not enc_started:
                    self.log_message('错误 - 无法访问ENC文件夹路径: Z:\DATA_BAK\ENC_BAK')

    def scan_existing_folders(self):
        """扫描已有文件夹并处理符合条件的文件"""
        self.log_message('开始扫描已有文件夹...')
        last_run_timestamp = self.data_processor.get_last_run_timestamp()

        # 扫描PC目录
        pc_path = 'Z:\DATA_BAK\QAR_PC'
        if os.path.exists(pc_path):
            for folder_name in os.listdir(pc_path):
                folder_path = os.path.join(pc_path, folder_name)
                if os.path.isdir(folder_path):
                    try:
                        created_time = os.path.getctime(folder_path)
                        if created_time > last_run_timestamp or last_run_timestamp == 0:
                            self.log_message(f'发现未处理的PC文件夹: {folder_name}')
                            threading.Thread(target=self.data_processor.process_folder, args=(folder_path, True)).start()
                    except Exception as e:
                        self.log_message(f'扫描PC文件夹 {folder_name} 时出错: {str(e)}')

        # 扫描ENC目录
        enc_path = 'Z:\DATA_BAK\ENC_BAK'
        if os.path.exists(enc_path):
            for folder_name in os.listdir(enc_path):
                folder_path = os.path.join(enc_path, folder_name)
                if os.path.isdir(folder_path):
                    try:
                        created_time = os.path.getctime(folder_path)
                        if created_time > last_run_timestamp or last_run_timestamp == 0:
                            self.log_message(f'发现未处理的ENC文件夹: {folder_name}')
                            threading.Thread(target=self.data_processor.process_folder, args=(folder_path, False)).start()
                    except Exception as e:
                        self.log_message(f'扫描ENC文件夹 {folder_name} 时出错: {str(e)}')

        self.log_message('已有文件夹扫描完成')

    def pause_monitoring(self):
        if self.is_running:
            self.status_var.set('监控已暂停')
            self.log_message('暂停监控...')
            self.pc_monitor.stop()
            self.enc_monitor.stop()
            self.is_running = False
            
            # 更新按钮状态（暂停时禁用暂停按钮，启用开始按钮）
            self.pause_button.config(state=tk.DISABLED)
            self.run_button.config(state=tk.NORMAL)
            
            self.log_message('监控已暂停')

    def start_schedule_thread(self):
        def run_schedule():
            while True:
                schedule.run_pending()
                time.sleep(1)

        schedule_thread = threading.Thread(target=run_schedule, daemon=True)
        schedule_thread.start()

    def exit_app(self):
        # 停止监控
        self.pause_monitoring()
        # 保存配置
        self.count_manager.set_pc_count(self.pc_count)
        self.count_manager.set_enc_count(self.enc_count)
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_manager.set_last_run_time(current_time)
        # 退出
        self.root.quit()
        self.root.destroy()

if __name__ == '__main__':
    root = tk.Tk()
    app = QARApp(root)
    root.mainloop()